import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";

const GooBackground: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const blobsRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Create floating blobs
    const createBlob = (index: number) => {
      const blob = document.createElement("div");
      blob.className = `absolute rounded-full bg-white/5 backdrop-blur-sm border border-white/10`;

      const size = Math.random() * 200 + 100;
      blob.style.width = `${size}px`;
      blob.style.height = `${size}px`;
      blob.style.left = `${Math.random() * 100}%`;
      blob.style.top = `${Math.random() * 100}%`;

      container.appendChild(blob);
      blobsRef.current[index] = blob;

      // GSAP animation for floating effect
      gsap.to(blob, {
        x: `+=${Math.random() * 400 - 200}`,
        y: `+=${Math.random() * 400 - 200}`,
        duration: Math.random() * 20 + 10,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
      });

      // Rotation animation
      gsap.to(blob, {
        rotation: 360,
        duration: Math.random() * 30 + 20,
        repeat: -1,
        ease: "none",
      });

      // Scale animation
      gsap.to(blob, {
        scale: Math.random() * 0.5 + 0.8,
        duration: Math.random() * 8 + 4,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
      });
    };

    // Create multiple blobs
    for (let i = 0; i < 8; i++) {
      createBlob(i);
    }

    return () => {
      blobsRef.current.forEach((blob) => {
        if (blob && blob.parentNode) {
          blob.parentNode.removeChild(blob);
        }
      });
      blobsRef.current = [];
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 overflow-hidden pointer-events-none z-0"
      style={{
        filter: "url(#goo)",
      }}
    >
      <svg className="absolute inset-0 w-0 h-0">
        <defs>
          <filter id="goo">
            <feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur" />
            <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -8" result="goo" />
            <feBlend in="SourceGraphic" in2="goo" />
          </filter>
        </defs>
      </svg>
    </div>
  );
};

export default GooBackground;
