import React, { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { gsap } from "gsap";
import { Code, Database, Globe, Smartphone, Server, Palette } from "lucide-react";

const Skills: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [activeCategory, setActiveCategory] = useState("all");
  const sectionRef = useRef<HTMLDivElement>(null);
  const skillsRef = useRef<HTMLDivElement>(null);
  const progressRefs = useRef<(HTMLDivElement | null)[]>([]);

  const skillCategories = [
    {
      id: "frontend",
      title: "Frontend",
      icon: Code,
      color: "from-blue-500 to-cyan-500",
      skills: [
        { name: "React", level: 95, color: "#61DAFB" },
        { name: "TypeScript", level: 90, color: "#3178C6" },
        { name: "Next.js", level: 85, color: "#000000" },
        { name: "Tailwind CSS", level: 95, color: "#06B6D4" },
        { name: "Vue.js", level: 80, color: "#4FC08D" },
        { name: "GSAP", level: 88, color: "#88CE02" },
      ],
    },
    {
      id: "backend",
      title: "Backend",
      icon: Server,
      color: "from-green-500 to-emerald-500",
      skills: [
        { name: "Node.js", level: 90, color: "#339933" },
        { name: "Python", level: 85, color: "#3776AB" },
        { name: "PostgreSQL", level: 80, color: "#336791" },
        { name: "MongoDB", level: 85, color: "#47A248" },
        { name: "GraphQL", level: 75, color: "#E10098" },
        { name: "Express.js", level: 88, color: "#000000" },
      ],
    },
    {
      id: "mobile",
      title: "Mobile",
      icon: Smartphone,
      color: "from-purple-500 to-pink-500",
      skills: [
        { name: "React Native", level: 82, color: "#61DAFB" },
        { name: "Flutter", level: 75, color: "#02569B" },
        { name: "iOS Development", level: 70, color: "#007AFF" },
        { name: "Android", level: 72, color: "#3DDC84" },
      ],
    },
    {
      id: "devops",
      title: "DevOps",
      icon: Database,
      color: "from-orange-500 to-red-500",
      skills: [
        { name: "Docker", level: 80, color: "#2496ED" },
        { name: "AWS", level: 75, color: "#FF9900" },
        { name: "Git", level: 95, color: "#F05032" },
        { name: "CI/CD", level: 85, color: "#326CE5" },
        { name: "Linux", level: 80, color: "#FCC624" },
      ],
    },
  ];

  const allSkills = skillCategories.flatMap((category) => category.skills);
  const filteredSkills =
    activeCategory === "all" ? allSkills : skillCategories.find((cat) => cat.id === activeCategory)?.skills || [];

  useEffect(() => {
    if (inView && sectionRef.current) {
      const tl = gsap.timeline();

      tl.fromTo(
        sectionRef.current.querySelector(".section-title"),
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: "power3.out" }
      );

      if (skillsRef.current) {
        tl.fromTo(
          skillsRef.current.children,
          { y: 80, opacity: 0, scale: 0.8 },
          { y: 0, opacity: 1, scale: 1, duration: 0.8, stagger: 0.1, ease: "back.out(1.7)" },
          "-=0.5"
        );
      }

      // Animate progress bars
      setTimeout(() => {
        progressRefs.current.forEach((ref, index) => {
          if (ref) {
            const progressBar = ref.querySelector(".progress-fill");
            const skill = filteredSkills[index];
            if (progressBar && skill) {
              gsap.fromTo(
                progressBar,
                { width: "0%" },
                {
                  width: `${skill.level}%`,
                  duration: 1.5,
                  ease: "power2.out",
                  delay: index * 0.1,
                }
              );
            }
          }
        });
      }, 500);
    }
  }, [inView, activeCategory, filteredSkills]);

  return (
    <section id="skills" className="py-32 bg-gradient-to-b from-black to-gray-900 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div ref={sectionRef}>
          <div className="section-title text-center mb-20">
            <h2 className="text-5xl sm:text-6xl font-bold text-white mb-8">
              My <span className="bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent">Skills</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-12">
              A comprehensive overview of my technical expertise and proficiency levels across different domains.
            </p>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-4 mb-16">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setActiveCategory("all")}
                className={`px-6 py-3 rounded-2xl font-medium transition-all duration-300 backdrop-blur-sm border ${
                  activeCategory === "all"
                    ? "bg-white text-black border-white"
                    : "bg-white/5 text-gray-300 border-white/10 hover:text-white hover:bg-white/10 hover:border-white/20"
                }`}
              >
                All Skills
              </motion.button>
              {skillCategories.map((category) => (
                <motion.button
                  key={category.id}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setActiveCategory(category.id)}
                  className={`flex items-center space-x-2 px-6 py-3 rounded-2xl font-medium transition-all duration-300 backdrop-blur-sm border ${
                    activeCategory === category.id
                      ? "bg-white text-black border-white"
                      : "bg-white/5 text-gray-300 border-white/10 hover:text-white hover:bg-white/10 hover:border-white/20"
                  }`}
                >
                  <category.icon size={18} />
                  <span>{category.title}</span>
                </motion.button>
              ))}
            </div>
          </div>

          {/* Skills Grid */}
          <div ref={skillsRef} className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <AnimatePresence>
              {filteredSkills.map((skill, index) => (
                <motion.div
                  key={`${skill.name}-${activeCategory}`}
                  layout
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  ref={(el) => (progressRefs.current[index] = el)}
                  className="group bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:border-white/20 hover:bg-white/10 transition-all duration-500 cursor-pointer"
                >
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-white group-hover:text-gray-100 transition-colors">
                      {skill.name}
                    </h3>
                    <span className="w-4 h-4 rounded-full" style={{ backgroundColor: skill.color }} />
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400 text-sm">Proficiency</span>
                      <span className="text-white font-medium">{skill.level}%</span>
                    </div>

                    <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                      <div
                        className="progress-fill h-full rounded-full transition-all duration-300"
                        style={{
                          background: `linear-gradient(90deg, ${skill.color}, ${skill.color}80)`,
                          width: "0%",
                        }}
                      />
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 1, delay: 1 }}
            className="mt-20 text-center"
          >
            <div className="inline-flex items-center space-x-12 bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10">
              <div className="text-center">
                <div className="text-4xl font-bold text-white mb-2">15+</div>
                <div className="text-gray-400">Technologies</div>
              </div>
              <div className="w-px h-16 bg-white/20" />
              <div className="text-center">
                <div className="text-4xl font-bold text-white mb-2">50+</div>
                <div className="text-gray-400">Projects</div>
              </div>
              <div className="w-px h-16 bg-white/20" />
              <div className="text-center">
                <div className="text-4xl font-bold text-white mb-2">5+</div>
                <div className="text-gray-400">Years</div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
