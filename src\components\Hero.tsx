import React, { useEffect, useRef, useState } from "react";
import { motion } from "framer-motion";
import { gsap } from "gsap";
import { TextPlugin } from "gsap/TextPlugin";
import { ChevronDown, Github, Linkedin, Mail, Download, ArrowRight } from "lucide-react";
import Hero3D from "./Hero3D";

gsap.registerPlugin(TextPlugin);

const Hero: React.FC = () => {
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const socialRef = useRef<HTMLDivElement>(null);
  const typingRef = useRef<HTMLSpanElement>(null);
  const [currentRole, setCurrentRole] = useState(0);

  const roles = ["Full Stack Developer", "React Specialist", "Node.js Expert", "UI/UX Enthusiast", "Problem Solver"];

  useEffect(() => {
    const tl = gsap.timeline({ delay: 1 });

    // Enhanced title animation with split text effect
    if (titleRef.current) {
      const titleElements = titleRef.current.children;
      gsap.set(titleElements, { y: 100, opacity: 0, rotationX: 90 });

      tl.to(titleElements, {
        y: 0,
        opacity: 1,
        rotationX: 0,
        duration: 1.2,
        stagger: 0.2,
        ease: "power3.out",
        transformOrigin: "center bottom",
      });
    }

    // Enhanced subtitle animation
    if (subtitleRef.current) {
      tl.fromTo(
        subtitleRef.current,
        { y: 50, opacity: 0, scale: 0.9 },
        { y: 0, opacity: 1, scale: 1, duration: 1, ease: "power3.out" },
        "-=0.8"
      );
    }

    // Enhanced button animations
    if (buttonsRef.current) {
      tl.fromTo(
        buttonsRef.current.children,
        { y: 30, opacity: 0, scale: 0.8 },
        { y: 0, opacity: 1, scale: 1, duration: 0.8, stagger: 0.15, ease: "back.out(1.7)" },
        "-=0.5"
      );
    }

    // Enhanced social icons animation
    if (socialRef.current) {
      tl.fromTo(
        socialRef.current.children,
        { scale: 0, opacity: 0, rotation: 180 },
        { scale: 1, opacity: 1, rotation: 0, duration: 0.6, stagger: 0.1, ease: "back.out(1.7)" },
        "-=0.3"
      );
    }
  }, []);

  // Typing effect for roles
  useEffect(() => {
    if (typingRef.current) {
      const typeRole = () => {
        gsap.to(typingRef.current, {
          text: roles[currentRole],
          duration: 1.5,
          ease: "none",
          onComplete: () => {
            setTimeout(() => {
              gsap.to(typingRef.current, {
                text: "",
                duration: 0.5,
                ease: "none",
                onComplete: () => {
                  setTimeout(() => {
                    setCurrentRole((prev) => (prev + 1) % roles.length);
                  }, 500);
                },
              });
            }, 2000);
          },
        });
      };

      setTimeout(typeRole, 2000);
    }
  }, [currentRole, roles]);

  const socialLinks = [
    { icon: Github, href: "#", label: "GitHub" },
    { icon: Linkedin, href: "#", label: "LinkedIn" },
    { icon: Mail, href: "#", label: "Email" },
  ];

  return (
    <section id="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden bg-black">
      <Hero3D />

      <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="mb-8"
        >
          <span className="inline-block px-6 py-3 rounded-full bg-white/5 backdrop-blur-sm border border-white/10 text-gray-300 text-sm font-medium">
            Welcome to my portfolio
          </span>
        </motion.div>

        <h1 ref={titleRef} className="text-6xl sm:text-7xl lg:text-8xl font-bold mb-8 leading-tight">
          <span className="block text-white mb-4">Hi, I'm</span>
          <span className="block bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">
            Alex Johnson
          </span>
        </h1>

        <div ref={subtitleRef} className="text-xl sm:text-2xl text-gray-400 mb-12 max-w-4xl mx-auto leading-relaxed">
          <p className="mb-4">
            A passionate{" "}
            <span ref={typingRef} className="text-white font-semibold min-h-[1em] inline-block">
              Full-Stack Developer
            </span>
          </p>
          <p>crafting exceptional digital experiences with modern technologies</p>
        </div>

        <div ref={buttonsRef} className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => document.getElementById("projects")?.scrollIntoView({ behavior: "smooth" })}
            className="group relative px-10 py-5 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm border border-white/20 text-white font-semibold rounded-2xl hover:from-white/20 hover:to-white/10 transition-all duration-500 shadow-xl hover:shadow-white/20 overflow-hidden"
          >
            <span className="relative z-10 flex items-center space-x-3">
              <span>View My Work</span>
              <ArrowRight size={20} className="group-hover:translate-x-1 transition-transform duration-300" />
            </span>
            <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="group relative px-10 py-5 bg-white text-black font-semibold rounded-2xl hover:bg-gray-100 transition-all duration-500 shadow-xl hover:shadow-2xl overflow-hidden"
          >
            <span className="relative z-10 flex items-center space-x-3">
              <Download size={20} className="group-hover:rotate-12 transition-transform duration-300" />
              <span>Download CV</span>
            </span>
            <div className="absolute inset-0 bg-gradient-to-r from-gray-100 to-white opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          </motion.button>
        </div>

        <div ref={socialRef} className="flex justify-center space-x-6 mb-20">
          {socialLinks.map((social, index) => (
            <motion.a
              key={index}
              href={social.href}
              whileHover={{ scale: 1.2, y: -4 }}
              whileTap={{ scale: 0.9 }}
              className="p-4 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 text-gray-400 hover:text-white hover:bg-white/10 transition-all duration-300 shadow-lg hover:shadow-white/5"
              aria-label={social.label}
            >
              <social.icon size={24} />
            </motion.a>
          ))}
        </div>

        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="text-gray-500"
        >
          <ChevronDown size={32} className="mx-auto" />
        </motion.div>
      </div>

      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/20 z-5" />
    </section>
  );
};

export default Hero;
