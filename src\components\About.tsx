import React, { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { gsap } from "gsap";
import { Code, Palette, Zap, Heart, Award, Users, Coffee, Lightbulb } from "lucide-react";

const About: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const sectionRef = useRef<HTMLDivElement>(null);
  const cardsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (inView && sectionRef.current) {
      const tl = gsap.timeline();

      tl.fromTo(
        sectionRef.current.querySelector(".section-title"),
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: "power3.out" }
      );

      if (cardsRef.current) {
        tl.fromTo(
          cardsRef.current.children,
          { y: 80, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.8, stagger: 0.2, ease: "power3.out" },
          "-=0.5"
        );
      }
    }
  }, [inView]);

  const features = [
    {
      icon: Code,
      title: "Clean Code",
      description:
        "Writing maintainable, scalable, and efficient code that follows industry best practices and standards.",
      color: "from-blue-500 to-cyan-500",
    },
    {
      icon: Palette,
      title: "Modern Design",
      description: "Creating beautiful, user-centered designs with attention to detail and accessibility.",
      color: "from-purple-500 to-pink-500",
    },
    {
      icon: Zap,
      title: "Performance",
      description: "Optimizing applications for speed, efficiency, and exceptional user experience.",
      color: "from-yellow-500 to-orange-500",
    },
    {
      icon: Heart,
      title: "Passion",
      description: "Bringing enthusiasm, creativity, and dedication to every project I work on.",
      color: "from-red-500 to-pink-500",
    },
  ];

  const stats = [
    { icon: Award, label: "Years Experience", value: "5+" },
    { icon: Users, label: "Happy Clients", value: "50+" },
    { icon: Code, label: "Projects Completed", value: "100+" },
    { icon: Coffee, label: "Cups of Coffee", value: "1000+" },
  ];

  return (
    <section id="about" className="py-32 bg-black relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div ref={sectionRef}>
          <div className="section-title text-center mb-20">
            <h2 className="text-5xl sm:text-6xl font-bold text-white mb-8">
              About <span className="bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent">Me</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
              I'm a passionate full-stack developer dedicated to creating innovative solutions and exceptional digital
              experiences.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
            <motion.div ref={ref} className="space-y-8">
              <div className="prose prose-lg text-gray-300">
                <p className="text-lg leading-relaxed mb-6">
                  With over 5 years of experience in software development, I've had the privilege of working on diverse
                  projects ranging from innovative startups to large-scale enterprise applications. My journey began
                  with a curiosity about how technology shapes our world, which led me to dive deep into programming.
                </p>
                <p className="text-lg leading-relaxed mb-6">
                  I specialize in full-stack development with expertise in React, Node.js, TypeScript, and modern cloud
                  technologies. I believe in writing clean, maintainable code and creating user experiences that not
                  only look stunning but also solve real-world problems effectively.
                </p>
                <p className="text-lg leading-relaxed">
                  When I'm not coding, you can find me exploring emerging technologies, contributing to open-source
                  projects, mentoring fellow developers, or sharing knowledge through technical writing and community
                  talks.
                </p>
              </div>

              {/* Enhanced Stats Grid */}
              <div className="grid grid-cols-2 gap-4">
                {stats.map((stat, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.05, y: -2 }}
                    className="group bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-500"
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-all duration-300">
                        <stat.icon size={16} className="text-white" />
                      </div>
                      <div className="text-2xl font-bold text-white">{stat.value}</div>
                    </div>
                    <div className="text-gray-400 text-sm">{stat.label}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <div ref={cardsRef} className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05, y: -8 }}
                  className="group bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 hover:border-white/20 transition-all duration-500 relative overflow-hidden"
                >
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                  />
                  <div
                    className={`w-16 h-16 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center mb-6 shadow-lg`}
                  >
                    <feature.icon size={28} className="text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-4 group-hover:text-gray-100 transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
