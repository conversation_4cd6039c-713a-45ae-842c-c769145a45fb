import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface LoadingScreenProps {
  onComplete: () => void;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ onComplete }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const tl = gsap.timeline({
      onComplete: () => {
        setTimeout(onComplete, 500);
      }
    });

    // Initial setup
    gsap.set([logoRef.current, textRef.current, progressRef.current], {
      opacity: 0,
      y: 50
    });

    // Animation sequence
    tl.to(logoRef.current, {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: "power3.out"
    })
    .to(textRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.5")
    .to(progressRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.6,
      ease: "power3.out"
    }, "-=0.3")
    .to(progressRef.current.querySelector('.progress-bar'), {
      width: "100%",
      duration: 2,
      ease: "power2.inOut"
    }, "-=0.2")
    .to(containerRef.current, {
      opacity: 0,
      duration: 0.8,
      ease: "power3.inOut"
    }, "+=0.5");

  }, [onComplete]);

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 bg-black z-50 flex items-center justify-center"
    >
      <div className="text-center">
        <div ref={logoRef} className="mb-8">
          <div className="w-20 h-20 mx-auto mb-4 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-white to-gray-400 rounded-2xl animate-pulse"></div>
            <div className="absolute inset-2 bg-black rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-xl">AJ</span>
            </div>
          </div>
        </div>
        
        <div ref={textRef} className="mb-8">
          <h1 className="text-2xl font-bold text-white mb-2">Alex Johnson</h1>
          <p className="text-gray-400">Full Stack Developer</p>
        </div>
        
        <div ref={progressRef} className="w-64 mx-auto">
          <div className="w-full h-1 bg-white/10 rounded-full overflow-hidden">
            <div className="progress-bar h-full bg-gradient-to-r from-white to-gray-400 w-0"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
