import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Sphere, MeshDistortMaterial, Float, Box } from '@react-three/drei';
import * as THREE from 'three';

const FloatingGeometry: React.FC<{ position: [number, number, number] }> = ({ position }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * 0.1;
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.15;
    }
  });

  return (
    <Float speed={1.5} rotationIntensity={0.5} floatIntensity={0.8}>
      <mesh ref={meshRef} position={position}>
        <icosahedronGeometry args={[1, 1]} />
        <MeshDistortMaterial
          color="#ffffff"
          attach="material"
          distort={0.3}
          speed={1.5}
          roughness={0.1}
          metalness={0.8}
          transparent
          opacity={0.1}
        />
      </mesh>
    </Float>
  );
};

const GlassBox: React.FC<{ position: [number, number, number] }> = ({ position }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * 0.05;
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.08;
    }
  });

  return (
    <Float speed={2} rotationIntensity={0.3} floatIntensity={1}>
      <Box ref={meshRef} position={position} args={[1.5, 1.5, 1.5]}>
        <meshPhysicalMaterial
          color="#ffffff"
          transparent
          opacity={0.05}
          roughness={0}
          metalness={0}
          transmission={0.9}
          thickness={0.5}
        />
      </Box>
    </Float>
  );
};

const ParticleField: React.FC = () => {
  const points = useRef<THREE.Points>(null);
  
  const particlesPosition = useMemo(() => {
    const positions = new Float32Array(2000 * 3);
    for (let i = 0; i < 2000; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 30;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 30;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 30;
    }
    return positions;
  }, []);

  useFrame((state) => {
    if (points.current) {
      points.current.rotation.x = state.clock.elapsedTime * 0.02;
      points.current.rotation.y = state.clock.elapsedTime * 0.01;
    }
  });

  return (
    <points ref={points}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particlesPosition.length / 3}
          array={particlesPosition}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial size={0.02} color="#ffffff" transparent opacity={0.6} />
    </points>
  );
};

const Hero3D: React.FC = () => {
  return (
    <div className="absolute inset-0 z-0">
      <Canvas camera={{ position: [0, 0, 12], fov: 60 }}>
        <ambientLight intensity={0.2} />
        <pointLight position={[10, 10, 10]} intensity={0.3} color="#ffffff" />
        <pointLight position={[-10, -10, -5]} color="#ffffff" intensity={0.2} />
        
        <ParticleField />
        
        <FloatingGeometry position={[-6, 3, -2]} />
        <FloatingGeometry position={[6, -2, -3]} />
        <FloatingGeometry position={[0, 4, -4]} />
        <FloatingGeometry position={[-3, -3, -1]} />
        
        <GlassBox position={[8, 1, -6]} />
        <GlassBox position={[-8, -1, -5]} />
        
        <Float speed={0.8} rotationIntensity={0.2} floatIntensity={0.3}>
          <Sphere args={[2, 64, 64]} position={[0, 0, -8]}>
            <meshPhysicalMaterial
              color="#ffffff"
              transparent
              opacity={0.03}
              roughness={0}
              metalness={0}
              transmission={0.95}
              thickness={2}
            />
          </Sphere>
        </Float>
      </Canvas>
    </div>
  );
};

export default Hero3D;