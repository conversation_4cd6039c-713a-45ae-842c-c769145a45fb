import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { Heart, Code } from 'lucide-react';

const Footer: React.FC = () => {
  const footerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (footerRef.current) {
      gsap.fromTo(footerRef.current.children,
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, stagger: 0.2, ease: "power3.out" }
      );
    }
  }, []);

  return (
    <footer ref={footerRef} className="bg-black border-t border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <h3 className="text-3xl font-bold bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent mb-6">
              Let's Build Something Amazing
            </h3>
            <p className="text-gray-400 max-w-md mx-auto text-lg leading-relaxed">
              Ready to bring your ideas to life? Let's collaborate and create exceptional digital experiences together.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex items-center justify-center space-x-3 text-gray-400 mb-8 text-lg"
          >
            <span>Made with</span>
            <Heart size={20} className="text-red-400 fill-current" />
            <span>and</span>
            <Code size={20} className="text-white" />
            <span>by Alex Johnson</span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-gray-500"
          >
            © {new Date().getFullYear()} Alex Johnson. All rights reserved.
          </motion.div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;