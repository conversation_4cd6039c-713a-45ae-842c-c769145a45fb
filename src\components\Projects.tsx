import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { gsap } from 'gsap';
import { ExternalLink, Github, X } from 'lucide-react';

interface Project {
  id: number;
  title: string;
  description: string;
  longDescription: string;
  image: string;
  technologies: string[];
  githubUrl: string;
  liveUrl: string;
  category: string;
}

const Projects: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [filter, setFilter] = useState('all');
  const sectionRef = useRef<HTMLDivElement>(null);
  const projectsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (inView && sectionRef.current) {
      const tl = gsap.timeline();
      
      tl.fromTo(sectionRef.current.querySelector('.section-title'),
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: "power3.out" }
      );
      
      if (projectsRef.current) {
        tl.fromTo(projectsRef.current.children,
          { y: 80, opacity: 0, scale: 0.9 },
          { y: 0, opacity: 1, scale: 1, duration: 0.8, stagger: 0.1, ease: "power3.out" },
          "-=0.5"
        );
      }
    }
  }, [inView, filter]);

  const projects: Project[] = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'A modern e-commerce platform built with React, Node.js, and Stripe integration.',
      longDescription: 'A comprehensive e-commerce solution featuring user authentication, product catalog, shopping cart, payment processing with Stripe, order management, and admin dashboard. Built with modern technologies and best practices.',
      image: 'https://images.pexels.com/photos/230544/pexels-photo-230544.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'Tailwind CSS'],
      githubUrl: '#',
      liveUrl: '#',
      category: 'fullstack',
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'A collaborative task management application with real-time updates.',
      longDescription: 'A productivity-focused task management application with team collaboration features, real-time updates using WebSocket, drag-and-drop functionality, file attachments, and detailed analytics dashboard.',
      image: 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['Vue.js', 'Express', 'Socket.io', 'PostgreSQL', 'Docker'],
      githubUrl: '#',
      liveUrl: '#',
      category: 'frontend',
    },
    {
      id: 3,
      title: 'Weather Dashboard',
      description: 'A beautiful weather dashboard with forecasts and interactive maps.',
      longDescription: 'An intuitive weather application providing current conditions, 7-day forecasts, interactive weather maps, location-based weather alerts, and historical weather data visualization.',
      image: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['React', 'D3.js', 'Weather API', 'CSS Grid', 'PWA'],
      githubUrl: '#',
      liveUrl: '#',
      category: 'frontend',
    },
    {
      id: 4,
      title: 'Analytics API',
      description: 'A scalable analytics API for tracking user behavior and generating insights.',
      longDescription: 'A robust analytics platform API that processes millions of events, provides real-time insights, custom dashboard creation, and advanced reporting capabilities with data visualization.',
      image: 'https://images.pexels.com/photos/590022/pexels-photo-590022.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['Python', 'FastAPI', 'Redis', 'PostgreSQL', 'Docker'],
      githubUrl: '#',
      liveUrl: '#',
      category: 'backend',
    },
    {
      id: 5,
      title: 'Social Media Dashboard',
      description: 'A comprehensive social media management dashboard.',
      longDescription: 'An all-in-one social media management platform that allows users to schedule posts, analyze engagement metrics, manage multiple accounts, and track brand mentions across various social platforms.',
      image: 'https://images.pexels.com/photos/267350/pexels-photo-267350.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['Next.js', 'GraphQL', 'Prisma', 'Tailwind CSS', 'Vercel'],
      githubUrl: '#',
      liveUrl: '#',
      category: 'fullstack',
    },
    {
      id: 6,
      title: 'Crypto Portfolio Tracker',
      description: 'A real-time cryptocurrency portfolio tracking application.',
      longDescription: 'A sophisticated crypto portfolio tracker with real-time price updates, portfolio performance analytics, price alerts, news integration, and advanced charting capabilities.',
      image: 'https://images.pexels.com/photos/8370752/pexels-photo-8370752.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['React', 'TypeScript', 'Chart.js', 'WebSocket', 'Material-UI'],
      githubUrl: '#',
      liveUrl: '#',
      category: 'frontend',
    },
  ];

  const categories = [
    { id: 'all', label: 'All Projects' },
    { id: 'frontend', label: 'Frontend' },
    { id: 'backend', label: 'Backend' },
    { id: 'fullstack', label: 'Full Stack' },
  ];

  const filteredProjects = filter === 'all' ? projects : projects.filter(project => project.category === filter);

  return (
    <section id="projects" className="py-32 bg-black relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div ref={sectionRef}>
          <div className="section-title text-center mb-20">
            <h2 className="text-5xl sm:text-6xl font-bold text-white mb-8">
              My <span className="bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent">Projects</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-12">
              A showcase of my recent work, featuring modern web applications and innovative solutions.
            </p>

            {/* Filter Buttons */}
            <div className="flex flex-wrap justify-center gap-4">
              {categories.map((category) => (
                <motion.button
                  key={category.id}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setFilter(category.id)}
                  className={`px-8 py-4 rounded-2xl font-medium transition-all duration-300 backdrop-blur-sm border ${
                    filter === category.id
                      ? 'bg-white text-black border-white'
                      : 'bg-white/5 text-gray-300 border-white/10 hover:text-white hover:bg-white/10 hover:border-white/20'
                  }`}
                >
                  {category.label}
                </motion.button>
              ))}
            </div>
          </div>

          <div ref={projectsRef} className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <AnimatePresence>
              {filteredProjects.map((project) => (
                <motion.div
                  key={project.id}
                  layout
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  whileHover={{ y: -10 }}
                  className="bg-white/5 backdrop-blur-sm rounded-3xl overflow-hidden border border-white/10 hover:border-white/20 transition-all duration-300 cursor-pointer group"
                  onClick={() => setSelectedProject(project)}
                  ref={ref}
                >
                  <div className="relative overflow-hidden">
                    <img
                      src={project.image}
                      alt={project.title}
                      className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  </div>
                  
                  <div className="p-8">
                    <h3 className="text-xl font-bold text-white mb-4">{project.title}</h3>
                    <p className="text-gray-400 mb-6 line-clamp-2 leading-relaxed">{project.description}</p>
                    
                    <div className="flex flex-wrap gap-2 mb-6">
                      {project.technologies.slice(0, 3).map((tech, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-white/10 text-gray-300 text-xs rounded-full border border-white/20"
                        >
                          {tech}
                        </span>
                      ))}
                      {project.technologies.length > 3 && (
                        <span className="px-3 py-1 bg-white/5 text-gray-400 text-xs rounded-full border border-white/10">
                          +{project.technologies.length - 3} more
                        </span>
                      )}
                    </div>
                    
                    <div className="flex space-x-4">
                      <a
                        href={project.githubUrl}
                        className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Github size={18} />
                        <span>Code</span>
                      </a>
                      <a
                        href={project.liveUrl}
                        className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <ExternalLink size={18} />
                        <span>Live</span>
                      </a>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Project Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="relative">
                <img
                  src={selectedProject.image}
                  alt={selectedProject.title}
                  className="w-full h-64 object-cover"
                />
                <button
                  onClick={() => setSelectedProject(null)}
                  className="absolute top-6 right-6 p-3 bg-black/50 backdrop-blur-sm rounded-2xl text-white hover:bg-black/70 transition-colors"
                >
                  <X size={24} />
                </button>
              </div>
              
              <div className="p-8">
                <h3 className="text-3xl font-bold text-white mb-6">{selectedProject.title}</h3>
                <p className="text-gray-300 mb-8 text-lg leading-relaxed">{selectedProject.longDescription}</p>
                
                <div className="mb-8">
                  <h4 className="text-xl font-semibold text-white mb-4">Technologies Used</h4>
                  <div className="flex flex-wrap gap-3">
                    {selectedProject.technologies.map((tech, index) => (
                      <span
                        key={index}
                        className="px-4 py-2 bg-white/10 text-gray-300 rounded-xl border border-white/20"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="flex space-x-4">
                  <a
                    href={selectedProject.githubUrl}
                    className="flex items-center space-x-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white rounded-2xl transition-colors border border-white/20"
                  >
                    <Github size={20} />
                    <span>View Code</span>
                  </a>
                  <a
                    href={selectedProject.liveUrl}
                    className="flex items-center space-x-2 px-6 py-3 bg-white text-black rounded-2xl hover:bg-gray-200 transition-all"
                  >
                    <ExternalLink size={20} />
                    <span>View Live</span>
                  </a>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default Projects;